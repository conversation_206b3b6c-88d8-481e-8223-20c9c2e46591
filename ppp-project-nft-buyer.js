#!/usr/bin/env node

import { Connection, Keypair, PublicKey } from '@solana/web3.js';
import { PPPBuyScript } from './ppp-buy-script.js';
import { WalletManager } from './wallet.js';
import { CONFIG } from './config/config.js';
import fetch from 'node-fetch';
import chalk from 'chalk';
import inquirer from 'inquirer';
import fs from 'fs';
import * as anchor from '@coral-xyz/anchor';

/**
 * PPP项目NFT购买器
 * 结合项目获取、NFT列表查询和购买功能
 */
class PPPProjectNFTBuyer {
    constructor() {
        this.baseUrl = CONFIG.PPP_API_BASE_URL;
        this.connection = null;
        this.walletManager = new WalletManager();
        this.pppBuyScript = null;
        this.anchorProgram=null;
    }

    /**
     * 初始化Solana连接和钱包
     */
    async initialize(rpcUrl = CONFIG.RPC_URL, privateKey = null, walletPath = null) {
        try {
            console.log(chalk.blue('🔗 初始化Solana连接...'));
            console.log(chalk.gray(`   RPC URL: ${rpcUrl}`));
            this.connection = new Connection(rpcUrl, CONFIG.CONFIRMATION_COMMITMENT);

            // 检查网络连接
            const version = await this.connection.getVersion();
            console.log(chalk.green(`✅ 连接成功! Solana版本: ${version['solana-core']}`));

            // 初始化钱包管理器
            this.walletManager.connection = this.connection;

            // 加载钱包
            let walletInitialized = false;

            if (privateKey) {
                // 从私钥初始化
                console.log(chalk.blue('🔑 从私钥初始化钱包...'));
                walletInitialized = this.walletManager.initFromPrivateKey(privateKey);
            } else if (walletPath && fs.existsSync(walletPath)) {
                // 从文件加载
                console.log(chalk.blue('🔑 从文件加载钱包...'));
                const secretKey = JSON.parse(fs.readFileSync(walletPath, 'utf8'));
                walletInitialized = this.walletManager.initFromPrivateKey(secretKey);
            } else {
                // 尝试自动加载已保存的钱包
                walletInitialized = this.walletManager.autoLoadWallet();
            }

            if (walletInitialized) {
                console.log(chalk.green(`✅ 钱包初始化成功: ${this.walletManager.getAddress()}`));

                // 检查余额
                const balance = await this.walletManager.getSolBalance();
                console.log(chalk.yellow(`💰 当前余额: ${balance.toFixed(4)} SOL`));

                if (balance < CONFIG.MIN_SOL_BALANCE * 10) {
                    console.log(chalk.red('⚠️  余额较低，请确保有足够的SOL用于交易'));
                }

                // 初始化PPP Buy脚本
                this.pppBuyScript = new PPPBuyScript(this.connection, this.walletManager.getKeypair());
                console.log(chalk.green('✅ PPP Buy脚本初始化完成'));
            } else {
                console.log(chalk.yellow('⚠️  钱包未初始化，将无法执行购买操作'));
            }


            // const wallet = new anchor.Wallet(this.walletManager.getKeypair());
    
            // // 创建 provider
            // const provider = new anchor.AnchorProvider(this.connection, wallet, {
            //   commitment: 'confirmed',
            // });
            //  this.anchorProgram = new anchor.Program.at(CONFIG.PPP_PROGRAM_ID, provider);

        } catch (error) {
            console.error(chalk.red(`初始化失败: ${error.message}`));
            throw error;
        }
    }

    /**
     * 获取项目列表
     */
    async getProjects(options = {}) {
        const { limit = 20, page = 1, keyword = '', sort = '' } = options;
        
        try {
            const url = `${this.baseUrl}/projects?limit=${limit}&page=${page}&keyword=${encodeURIComponent(keyword)}&sort=${sort}`;
            console.log(chalk.blue(`📋 获取项目列表: ${url}`));

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'User-Agent': 'PPP-Project-NFT-Buyer/1.0.0'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            if (!data.success) {
                throw new Error('API 返回失败状态');
            }

            return data;
        } catch (error) {
            console.error(chalk.red(`获取项目列表失败: ${error.message}`));
            throw error;
        }
    }

    /**
     * 获取项目的NFT列表
     */
    async getProjectNFTs(projectMint, options = {}) {
        const {
            limit = 100,
            page = 1,
            sort = 'last_trade',
            nft_id = '',
            owner_pubkey = '',
            order = 'asc'
        } = options;

        try {
            const url = `${this.baseUrl}/project/${projectMint}/nfts?limit=${limit}&page=${page}&sort=${sort}&nft_id=${nft_id}&owner_pubkey=${encodeURIComponent(owner_pubkey)}&order=${order}`;
            console.log(chalk.blue(`🎨 获取NFT列表: ${url}`));

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'User-Agent': 'PPP-Project-NFT-Buyer/1.0.0'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            if (!data.success) {
                throw new Error('API 返回失败状态');
            }

            return data;
        } catch (error) {
            console.error(chalk.red(`获取NFT列表失败: ${error.message}`));
            throw error;
        }
    }

    /**
     * 显示项目信息
     */
    displayProject(project, index) {
        console.log(chalk.blue.bold(`\n📊 项目 ${index + 1}: ${project.project_name}`));
        console.log('═'.repeat(60));
        
        console.log(chalk.green.bold('🏷️  基本信息:'));
        console.log(`项目名称: ${project.project_name}`);
        console.log(`代币符号: ${project.token_symbol}`);
        console.log(`项目地址: ${project.project_mint}`);
        
        console.log(chalk.green.bold('\n🎨 NFT 信息:'));
        console.log(`NFT 名称: ${project.nft_name}`);
        console.log(`已发行: ${project.nft_issue_count}`);
        console.log(`已销毁: ${project.nft_burn_count}`);
        console.log(`活跃NFT: ${project.nft_issue_count - project.nft_burn_count}`);
        
        console.log(chalk.green.bold('\n📈 市场数据:'));
        console.log(`市值: $${this.formatNumber(project.market_cap)}`);
        console.log(`24h 交易量: $${this.formatNumber(project.volume_24h)}`);
        console.log(`24h 交易次数: ${project.tx_24h}`);
    }

    /**
     * 显示NFT信息
     */
    displayNFT(nft, index) {
        const priceSol = nft.price / 1000000000;
        
        console.log(chalk.cyan.bold(`\n🎨 NFT ${index + 1} (ID: ${nft.nft_id})`));
        console.log('─'.repeat(50));
        
        console.log(chalk.green(`NFT地址: ${nft.nft_pubkey}`));
        console.log(chalk.green(`持有者: ${nft.owner_pubkey}`));
        console.log(chalk.yellow(`当前价格: ${priceSol.toFixed(6)} SOL (${this.formatNumber(nft.price)} lamports)`));
        console.log(chalk.blue(`轮次: ${nft.round}`));
        console.log(chalk.blue(`分割次数: ${nft.split_count}`));
        console.log(chalk.gray(`最后交易: ${new Date(nft.last_trade * 1000).toLocaleString()}`));
        
        const isActive = nft.is_burned === 0;
        console.log(`状态: ${isActive ? chalk.green('活跃') : chalk.red('已销毁')}`);
    }

    /**
     * 确保PPP Buy脚本已初始化
     */
    ensurePPPBuyScriptInitialized() {
        if (!this.pppBuyScript && this.walletManager.getKeypair()) {
            this.pppBuyScript = new PPPBuyScript(this.connection, this.walletManager.getKeypair());
            console.log(chalk.green('✅ PPP Buy脚本已初始化'));
        }
    }

    /**
     * 获取钱包信息
     */
    async getWalletInfo() {
        if (!this.walletManager.getAddress()) {
            return null;
        }

        const address = this.walletManager.getAddress();
        const solBalance = await this.walletManager.getSolBalance();

        // 确保PPP Buy脚本已初始化
        this.ensurePPPBuyScriptInitialized();

        return {
            address,
            solBalance,
            isInitialized: true
        };
    }

    /**
     * 检查购买条件
     */
    async checkBuyConditions(nft) {
        if (!this.walletManager.getAddress()) {
            throw new Error('钱包未初始化');
        }

        if (nft.is_burned === 1) {
            throw new Error('NFT已被销毁，无法购买');
        }

        const priceSol = nft.price / 1000000000;
        const hasEnoughBalance = await this.walletManager.checkSufficientBalance(
            CONFIG.TOKENS.SOL.mint,
            priceSol,
            CONFIG.TOKENS.SOL.decimals
        );

        if (!hasEnoughBalance) {
            throw new Error(`余额不足。需要: ${priceSol.toFixed(6)} SOL + 手续费`);
        }

        return true;
    }

    /**
     * 购买NFT
     */
    async buyNFT(nft, projectMint) {


         console.log(JSON.stringify(nft));
         console.log("+++++++++++++++++++++++++++++++++++++++++++++++");
         
        // 确保PPP Buy脚本已初始化
        this.ensurePPPBuyScriptInitialized();

        if (!this.pppBuyScript) {
            throw new Error('PPP Buy脚本未初始化，请先加载钱包');
        }

        // 检查购买条件
        await this.checkBuyConditions(nft);

        try {
            console.log(chalk.blue.bold(`\n🛒 准备购买NFT ${nft.nft_id}...`));
            
            const priceSol = nft.price / 1000000000;
            console.log(`NFT地址: ${nft.nft_pubkey}`);
            console.log(`当前价格: ${priceSol.toFixed(6)} SOL`);
            console.log(`当前持有者: ${nft.owner_pubkey}`);

            // 构建购买参数 (基于真实交易记录，价格由程序自动计算)
            // 所有账户地址现在都在ppp-buy-script.js中硬编码，只需要传递NFT和拥有者信息
            const buyParams = {
                mintpubkey: nft.mint_pubkey,
                nftPubkey: nft.nft_pubkey,
                previousOwner: nft.owner_pubkey
            };

            // 确认购买
            const { confirm } = await inquirer.prompt([
                {
                    type: 'confirm',
                    name: 'confirm',
                    message: `确认购买NFT ${nft.nft_id} (${priceSol.toFixed(6)} SOL)?`,
                    default: false
                }
            ]);

            if (!confirm) {
                console.log(chalk.yellow('❌ 购买已取消'));
                return null;
            }

            console.log(chalk.blue('🔄 执行购买交易...'));
            const result = await this.pppBuyScript.executeBuy(buyParams);
            
            console.log(chalk.green.bold('✅ 购买成功!'));
            console.log(`交易签名: ${result.signature}`);
            console.log(`NFT地址: ${result.nftPubkey}`);
            console.log(`购买金额: ${result.amount} SOL`);
            console.log(`项目地址: ${projectMint}`);

            return {
                ...result,
                projectMint,
                nft: nft,
                buyer: this.walletManager.getAddress()
            };

        } catch (error) {
            console.error(chalk.red(`购买失败: ${error.message}`));
            throw error;
        }
    }

    /**
     * 格式化数字
     */
    formatNumber(num) {
        if (!num) return '0';
        return new Intl.NumberFormat().format(num);
    }

    /**
     * 保存数据到日志文件夹
     */
    saveData(data, filename) {
        try {
            // 创建日志文件夹
            const logDir = './logs';
            if (!fs.existsSync(logDir)) {
                fs.mkdirSync(logDir, { recursive: true });
                console.log(chalk.blue(`📁 创建日志文件夹: ${logDir}`));
            }

            // 构建完整的文件路径
            const fullPath = `${logDir}/${filename}`;

            // 保存数据
            fs.writeFileSync(fullPath, JSON.stringify(data, null, 2));
            console.log(chalk.green(`✅ 数据已保存到: ${fullPath}`));
            return fullPath;
        } catch (error) {
            console.error(chalk.red(`保存失败: ${error.message}`));
            return null;
        }
    }
}

export { PPPProjectNFTBuyer };
