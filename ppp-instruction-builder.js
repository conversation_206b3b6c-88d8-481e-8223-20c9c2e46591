import { Connection, PublicKey, SystemProgram } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID, getAssociatedTokenAddress } from '@solana/spl-token';
import BN from 'bn.js';
import { CONFIG } from './config/config.js';

/**
 * PPP指令构建器 - 基于IDL动态生成Buy指令参数
 * 替代硬编码的账户配置
 */
class PPPInstructionBuilder {
   constructor() {

      // 固定常量 (基于IDL和文档)
      this.programId = new PublicKey(CONFIG.PPP_PROGRAM_ID);
      this.CP_SWAP_PROGRAM = new PublicKey('CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C');
      this.AMM_CONFIG = new PublicKey('D4FPEruKEHrG5TenZ2mpDGEfu1iUvTiqBxvpU8HLBvC2'); // 正确的AMM配置地址
      this.WSOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');
      this.PROTOCOL_FEE = new PublicKey('DFShmtUPnJaxKSX6wg2zbyWEAVs9HuBeKNeGihg8o2oV');
      this.SYSTEM_PROGRAM = SystemProgram.programId;
      this.TOKEN_PROGRAM = TOKEN_PROGRAM_ID;
   }

   /**
    * 计算PDA地址
    */
   async calculatePDAAddresses(mintPubkey, projectPubkey, nftId) {
      const mintPubkeyObj = new PublicKey(mintPubkey);
      const projectPubkeyObj = new PublicKey(projectPubkey);

      // 1. Project PDA: 直接使用提供的project_pubkey
      const projectPda = projectPubkeyObj;

      // 2. NFT PDA: ["ppp_nft", nft_id_le_bytes, project_pda_bytes]
      const nftIdBytes = new BN(nftId).toArrayLike(Buffer, 'le', 8);
      const [nftPda] = PublicKey.findProgramAddressSync(
         [Buffer.from("ppp_nft"), nftIdBytes, projectPda.toBuffer()],
         this.programId
      );

      // 3. Pool Auth PDA: ["ppp_auth", mint_pubkey_bytes] - 使用mint_pubkey
      const [poolAuthPda] = PublicKey.findProgramAddressSync(
         [Buffer.from("ppp_auth"), mintPubkeyObj.toBuffer()],
         this.programId
      );

      return {
         projectPda,
         nftPda,
         poolAuthPda
      };
   }

   /**
    * 计算Raydium相关账户
    */
   async calculateRaydiumAccounts(projectMint) {
      const projectMintPubkey = new PublicKey(projectMint);

      // 代币排序 (字节序比较)
      const token0 = this.WSOL_MINT;
      const token1 = projectMintPubkey;

      // 确保正确的代币顺序
      const [tokenA, tokenB] = this.sortTokens(token0, token1);

      // 4. Authority PDA (Raydium): ["vault_and_lp_mint_auth_seed"]
      const [authorityPda] = PublicKey.findProgramAddressSync(
         [Buffer.from("vault_and_lp_mint_auth_seed")],
         this.CP_SWAP_PROGRAM
      );

      // 5. Pool State PDA: ["pool", amm_config_bytes, token0_bytes, token1_bytes]
      const [poolStatePda] = PublicKey.findProgramAddressSync(
         [
            Buffer.from("pool"),
            this.AMM_CONFIG.toBuffer(),
            tokenA.toBuffer(),
            tokenB.toBuffer()
         ],
         this.CP_SWAP_PROGRAM
      );

      // 6. Input Vault PDA: ["pool_vault", pool_state_bytes, token_mint_bytes]
      const [inputVaultPda] = PublicKey.findProgramAddressSync(
         [
            Buffer.from("pool_vault"),
            poolStatePda.toBuffer(),
            tokenA.toBuffer()
         ],
         this.CP_SWAP_PROGRAM
      );

      // 7. Output Vault PDA
      const [outputVaultPda] = PublicKey.findProgramAddressSync(
         [
            Buffer.from("pool_vault"),
            poolStatePda.toBuffer(),
            tokenB.toBuffer()
         ],
         this.CP_SWAP_PROGRAM
      );

      // 8. Observation PDA: ["observation", pool_state_bytes]
      const [observationPda] = PublicKey.findProgramAddressSync(
         [Buffer.from("observation"), poolStatePda.toBuffer()],
         this.CP_SWAP_PROGRAM
      );

      return {
         authorityPda,
         poolStatePda,
         inputVaultPda,
         outputVaultPda,
         observationPda,
         tokenA,
         tokenB
      };
   }

   /**
    * 计算关联代币账户
    */
   async calculateTokenAccounts(buyer, projectMint) {
      const projectMintPubkey = new PublicKey(projectMint);

      // 买家的WSOL关联代币账户
      const buyerWsolAta = await getAssociatedTokenAddress(
         this.WSOL_MINT,
         buyer
      );

      // 买家的项目代币关联代币账户
      const buyerProjectAta = await getAssociatedTokenAddress(
         projectMintPubkey,
         buyer
      );

      return {
         buyerWsolAta,
         buyerProjectAta
      };
   }


   /**
    * 
    * 构建完整的Burn指令账户列表 10个地址
    */

   async bulidBurnInstructionAccounts(params) {

      const { project_pubkey, mint_pubkey, nft_id, Payer, } = params;
      // 1. 计算PDA地址
      const pdaAddresses = await this.calculatePDAAddresses(mint_pubkey, project_pubkey, nft_id);


      // console.log(`${pdaAddresses}`);

      console.log('✅ PDA地址计算完成');

      // 2. 计算Raydium账户
      const raydiumAccounts = await this.calculateRaydiumAccounts(mint_pubkey);
      console.log('✅ Raydium账户计算完成');

      // 3. 计算关联代币账户
      const tokenAccounts = await this.calculateTokenAccounts(Payer, mint_pubkey);
      console.log('✅ 代币账户计算完成');

      // 4. 计算Pool相关的关联代币账户 (基于pool_auth PDA)
      const poolWsolVault = await getAssociatedTokenAddress(
         this.WSOL_MINT,
         pdaAddresses.poolAuthPda,
         true // allowOwnerOffCurve
      );

      console.log('✅ Pool WSOL金库计算完成');

      // 5. 计算Pool Auth的项目代币关联代币账户
      const poolAuthProjectAta = await getAssociatedTokenAddress(
         new PublicKey(mint_pubkey),
         pdaAddresses.poolAuthPda,
         true // allowOwnerOffCurve
      );

      console.log("++++++++++++++++")
      // 5. 构建完整的账户列表 (基于实际Ani数组的25个账户顺序)
      const accounts = [
         // 0: nft
         { pubkey: pdaAddresses.nftPda, isSigner: false, isWritable: true },


         // 1: Authority
         { pubkey: pdaAddresses.poolAuthPda, isSigner: false, isWritable: true },

         // 2: mint_pubkey
         { pubkey: new PublicKey(mint_pubkey), isSigner: false, isWritable: false },

         // 3: Output Vault:
         { pubkey: poolAuthProjectAta, isSigner: false, isWritable: true },

         // 4: 资产账户
         { pubkey: tokenAccounts.buyerProjectAta, isSigner: false, isWritable: true },

         // 5: Project
         { pubkey: new PublicKey(project_pubkey), isSigner: false, isWritable: true },

         // 6: Payer
         { pubkey: new PublicKey(Payer), isSigner: false, isWritable: true },

         // 7: Token Program
         { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
         // 8: Associated Token Program:
         { pubkey: new PublicKey('ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL'), isSigner: false, isWritable: false },
         // 9: System Program:
         { pubkey: new PublicKey('11111111111111111111111111111111'), isSigner: false, isWritable: false },

      ];


      return accounts;

   }


     /**
   * 构建Buy指令账户列表
   * @param {Object} params - 参数对象
   * @param {string} params.project_pubkey - 项目公钥
   * @param {string} params.mint_pubkey - 代币铸造公钥
   * @param {number} params.nft_id - NFT ID
   * @param {PublicKey} params.buyer - 买家公钥
   * @param {string} params.owner_pubkey - 当前拥有者公钥
   * @param {string|null} params.referral - 推荐人公钥（可选）
   * @param {PublicKey} params.creator_pubkey - 创建者公钥
   * @returns {Object} 包含账户列表和相关数据的对象
   */
   async buildBuyInstructionAccounts(params) {
      const { project_pubkey, mint_pubkey, nft_id, buyer, owner_pubkey, creator_pubkey,nft_pubkey,authority_pubkey} = params;


      const referral = CONFIG.REFERRAL;
      try {

         // console.log('🔧 开始计算Buy指令账户...');
         // console.log(`邀请人:${referral}`)

         // 1. 计算PDA地址
        const pdaAddresses = await this.calculatePDAAddresses(mint_pubkey, project_pubkey, nft_id);

        const [authorityPda] = PublicKey.findProgramAddressSync(
            [Buffer.from("ppp_auth"), new PublicKey(mint_pubkey).toBuffer()],
            this.programId
         );

        // const [pdaAddress, bump] = PublicKey.findProgramAddressSync(seeds, programId);

         // console.log(`mint_pubkey:${mint_pubkey}`)
         // console.log(`authorityPda:${authorityPda.toString()}`)
         // console.log('✅ PDA地址计算完成');


         //按 组合1 来计算

         // console.log("++++++++++++++++")
    
            // 按照IDL规范构建9个账户
            const idlAccounts = [
               // 1. payer (买家，可写入，签名者)
               { pubkey: buyer, isSigner: true, isWritable: true },
               
               // 2. project (可写入)
               { pubkey: new PublicKey(project_pubkey), isSigner: false, isWritable: true },
               
               // 3. authority (PDA，可写入)
               { pubkey: pdaAddresses.poolAuthPda , isSigner: false, isWritable: true },
               
               // 4. nft (可写入)
               { pubkey: pdaAddresses.nftPda, isSigner: false, isWritable: true },
               
               // 5. current_owner (可写入)
               { pubkey: new PublicKey(owner_pubkey), isSigner: false, isWritable: true },
               
               // 6. protocol_fee (固定地址，可写入)
               { pubkey: this.PROTOCOL_FEE, isSigner: false, isWritable: true },
               
               // 7. referral (可写入)
               { pubkey: referral ? new PublicKey(referral) : buyer, isSigner: false, isWritable: true },
               
               // 8. creator (可写入)
               { pubkey: creator_pubkey, isSigner: false, isWritable: true },
               
               // 9. system_program (固定地址)
               { pubkey: this.SYSTEM_PROGRAM, isSigner: false, isWritable: false }
            ];
            
            // console.log(`✅ 构建完成，共 ${idlAccounts.length} 个账户 (IDL格式)`);
            
            // // 验证账户数量
            // if (idlAccounts.length !== 9) {
            //    console.warn(`⚠️ IDL账户数量不匹配: ${idlAccounts.length}/9`);
            // }
            
            return {
               accounts: idlAccounts,
               isIdlFormat: true
            };
        

      } catch (err) {
         console.log('err:', err);
         throw err; // 重新抛出错误，让调用者处理
      }
   }

   /**
    * 代币排序 (字节序比较)
    */
   sortTokens(tokenA, tokenB) {
      const bufferA = tokenA.toBuffer();
      const bufferB = tokenB.toBuffer();

      for (let i = 0; i < 32; i++) {
         if (bufferA[i] < bufferB[i]) {
            return [tokenA, tokenB];
         } else if (bufferA[i] > bufferB[i]) {
            return [tokenB, tokenA];
         }
      }

      return [tokenA, tokenB]; // 相等的情况
   }

   /**
    * 验证账户配置 (基于IDL要求)
    */
   async validateAccounts(accounts, expectedAccounts = null) {
      console.log('🔍 验证账户配置...');

      const validation = {
         totalAccounts: accounts.length,
         signerCount: accounts.filter(acc => acc.isSigner).length,
         writableCount: accounts.filter(acc => acc.isWritable).length,
         issues: []
      };

      // 检查账户总数 (IDL要求25个)
      if (validation.totalAccounts !== 25) {
         validation.issues.push(`账户总数异常: ${validation.totalAccounts} (IDL要求: 25)`);
      }

      // 检查签名者数量 (应该只有买家)
      if (validation.signerCount !== 1) {
         validation.issues.push(`签名者数量异常: ${validation.signerCount} (期望: 1)`);
      }

      // 检查买家是否为签名者 (索引1: payer)
      if (!accounts[1]?.isSigner) {
         validation.issues.push('买家账户(索引1)未设置为签名者');
      }

      // 检查关键账户的可写性
      const writableIndices = [1, 4, 5, 6, 7, 8, 13, 16, 17, 18, 20, 21, 22, 23, 24];
      writableIndices.forEach(index => {
         if (accounts[index] && !accounts[index].isWritable) {
            validation.issues.push(`账户 ${index} 应该是可写的`);
         }
      });

      // 如果提供了期望值，进行对比
      if (expectedAccounts) {
         for (let i = 0; i < Math.min(accounts.length, expectedAccounts.length); i++) {
            if (accounts[i].pubkey.toString() !== expectedAccounts[i]) {
               validation.issues.push(`账户 ${i} 不匹配: ${accounts[i].pubkey.toString()} vs ${expectedAccounts[i]}`);
            }
         }
      }

      console.log(`✅ 验证完成: ${validation.issues.length === 0 ? '通过' : '发现问题'}`);

      return validation;
   }

   /**
    * 生成调试信息 (包含IDL账户映射)
    */
   generateDebugInfo(buildResult) {
      const { accounts, pdaAddresses, raydiumAccounts, tokenAccounts, poolWsolVault } = buildResult;

      return;
      // IDL账户名称映射
      const idlAccountNames = [
         'cp_swap_program', 'payer', 'authority', 'amm_config', 'pool_state',
         'input_token_account', 'output_token_account', 'input_vault', 'output_vault',
         'input_token_program', 'output_token_program', 'input_token_mint', 'output_token_mint',
         'observation_state', 'system_program', 'token_program', 'project', 'nft',
         'current_owner', 'wsol_mint', 'pool_wsol_vault', 'pool_auth', 'protocol_fee',
         'referral', 'creator'
      ];

      return {
         summary: {
            totalAccounts: accounts.length,
            expectedAccounts: 25,
            signerCount: accounts.filter(acc => acc.isSigner).length,
            writableCount: accounts.filter(acc => acc.isWritable).length,
            isValid: accounts.length === 25
         },
         pdaAddresses: {
            projectPda: pdaAddresses.projectPda.toString(),
            nftPda: pdaAddresses.nftPda.toString(),
            poolAuthPda: pdaAddresses.poolAuthPda.toString()
         },
         raydiumAccounts: {
            authority: raydiumAccounts.authorityPda.toString(),
            poolState: raydiumAccounts.poolStatePda.toString(),
            inputVault: raydiumAccounts.inputVaultPda.toString(),
            outputVault: raydiumAccounts.outputVaultPda.toString(),
            observation: raydiumAccounts.observationPda.toString()
         },
         tokenAccounts: {
            buyerWsol: tokenAccounts.buyerWsolAta.toString(),
            buyerProject: tokenAccounts.buyerProjectAta.toString(),
            poolWsolVault: poolWsolVault.toString()
         },
         accountList: accounts.map((acc, index) => ({
            index,
            idlName: idlAccountNames[index] || `unknown_${index}`,
            pubkey: acc.pubkey.toString(),
            isSigner: acc.isSigner,
            isWritable: acc.isWritable
         }))
      };
   }
}

export { PPPInstructionBuilder };
