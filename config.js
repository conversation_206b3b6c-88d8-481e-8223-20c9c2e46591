// 定时购买配置文件
export const config = {
    // 需要定时购买的项目列表
    projects: [
        // 'iNxCAjhbc4L5jVEe3ajHdY28rqcNBGPpqMVkiEZZppp', // Rich
        '4TStMHaXQhkqvFRXq8dmMLyP3VYmkdAuCYBBPMv2Wppp', // PPP
    //    'G8eETgZz7YNYJ1xLi7S6LkcuLF8jQqiJJxy9Rcd82ppp',  // DOIT
        // '7f2QWEWFh8R5NyTHusF3QQ8SE6vjfMabrQoYv2cXGppp'  // CAT
    ],

    // 购买配置
    buyConfig: {
        maxNFTsPerTx: 2,        // 每个交易打包的NFT数量
        nftCountPerProject: 80,   // 每个项目购买的NFT数量
        maxRetries: 1,           // 最大重试次数
        batchMode: 'concurrent', // 批次处理模式: 'sequential' 或 'concurrent'
        advanceSeconds: 30 ,     // 提前启动毫秒数
        concurrencyLimit: 8,    // 并发限制

        // NFT选择策略配置
        selectionStrategy: 'random', // 选择策略: 'random', 'cheapest', 'expensive', 'mixed'
        randomSeed: null,       // 随机种子，null表示使用当前时间
        mixedRatio: {           // 混合策略比例配置
            cheap: 0.6,         // 便宜NFT占比 (60%)
            random: 0.4         // 随机NFT占比 (40%)
        }
    },

    // 数据缓存配置
    cache: {
        enabled: true,           // 是否启用缓存
        updateInterval: 300,     // 缓存更新间隔（秒）
        dataDir: './cache'       // 缓存目录
    },

    // 日志配置
    logging: {
        enabled: true,
        logDir: './logs',
        logLevel: 'info'         // 'debug', 'info', 'warn', 'error'
    }
};

export default config;
