import { Connection, PublicKey, Transaction, SystemProgram, LAMPORTS_PER_SOL, TransactionInstruction } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID, getAssociatedTokenAddress, createAssociatedTokenAccountInstruction } from '@solana/spl-token';
import BN from 'bn.js';
import chalk from 'chalk';
import { PPPInstructionBuilder } from './ppp-instruction-builder.js';
import { ASSOCIATED_TOKEN_PROGRAM_ID } from '@solana/spl-token';
import { CONFIG } from './config/config.js';

// PPP程序配置
const PPP_PROGRAM_ID = new PublicKey(CONFIG.PPP_PROGRAM_ID);
const RAYDIUM_AMM_PROGRAM_ID = new PublicKey('CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C');
const WSOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');

class PPPBuyScript {
    constructor(connection, wallet) {
        this.connection = connection;
        this.wallet = wallet;
    }

    /**
     * 执行PPP程序的buy操作
     * @param {Object} params - 购买参数
     * @param {string} params.nftPubkey - NFT公钥
     * @param {string} params.previousOwner - 上一轮拥有者公钥
     * @param {number} params.amount - 购买金额 (SOL)
     */
    async executeBuy(params) {


        try {
            const { mintpubkey, nftPubkey, previousOwner } = params;

            console.log('开始执行PPP Buy操作...');
            console.log(`NFT: ${nftPubkey}`);
            // console.log(`当前买家: ${this.wallet.publicKey.toString()}`);
            // console.log(`上一轮拥有者: ${previousOwner}`);
            // console.log('注意: 价格由PPP程序自动计算，无需在指令中指定');

            // 构建交易
            const transaction = new Transaction();

            // 构建PPP Buy指令 (不需要金额参数，由程序自动计算)
            const buyInstruction = await this.createBuyInstruction({
                mintpubkey: new PublicKey(mintpubkey),
                nftPubkey: new PublicKey(nftPubkey),
                buyer: this.wallet.publicKey,
                previousOwner: new PublicKey(previousOwner),
                nft_id: nft.nft_id
            });

            transaction.add(buyInstruction);
            // console.log(JSON.stringify(transaction));
            // 获取最新的区块哈希
            const { blockhash } = await this.connection.getLatestBlockhash();
            transaction.recentBlockhash = blockhash;
            transaction.feePayer = this.wallet.publicKey;

            // 签名交易
            transaction.sign(this.wallet);

            // 发送交易
            console.log('发送交易中...');
            const signature = await this.connection.sendRawTransaction(transaction.serialize());

            console.log(`交易签名: ${signature}`);

            // 等待确认
            const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');

            if (confirmation.value.err) {
                throw new Error(`交易失败: ${confirmation.value.err}`);
            }

            console.log('交易成功确认!');
            return {
                signature,
                success: true,
                nftPubkey,
                amount,
                buyer: this.wallet.publicKey.toString()
            };

        } catch (error) {
            // console.error('Buy操作失败:', error);
            throw error;
        }
    }

    /**
     * 批量执行PPP程序的buy操作 - 将多个NFT打包到一个交易中
     * @param {Array} nftList - NFT列表，每个元素包含 {mintpubkey, nftPubkey, previousOwner}
     * @param {Object} options - 批量购买选项
     * @param {number} options.maxNFTsPerTx - 每个交易包含的最大NFT数量 (默认3，避免交易过大)
     * @param {number} options.maxRetries - 最大重试次数 (默认3)
     * @param {string} options.batchMode - 批次处理模式: 'sequential'(逐个) 或 'concurrent'(并发)
     * @param {number} options.concurrencyLimit - 并发模式下的最大同时交易数 (默认10)
     */
    async executeBatchBuy(nftList, options = {}) {
        const { maxNFTsPerTx = 3, maxRetries = 2, batchMode = 'sequential', concurrencyLimit = 5 } = options;

        console.log(chalk.blue.bold(`🚀 开始批量购买 ${nftList.length} 个NFT`));
        console.log(`每个交易最多包含: ${maxNFTsPerTx} 个NFT`);
        console.log(`最大重试: ${maxRetries} 次`);
        console.log(`处理模式: ${batchMode === 'concurrent' ? '⚡ 并发处理批次' : '🔄 逐个处理批次'}`);
        if (batchMode === 'concurrent' && concurrencyLimit) {
            console.log(`并发限制: 最多同时 ${concurrencyLimit} 个交易`);
        }
        console.log('');

        const results = [];
        const batches = [];

        // 将NFT列表分组为批次，每个批次打包到一个交易中
        for (let i = 0; i < nftList.length; i += maxNFTsPerTx) {
            batches.push(nftList.slice(i, i + maxNFTsPerTx));
        }

        console.log(chalk.blue(`📦 将创建 ${batches.length} 个打包交易`));
        // 记录开始时间
        const startTime = new Date().getTime();

        if (batchMode === 'concurrent') {
            // 并发处理所有批次
            await this.processBatchesConcurrently(batches, maxRetries, results, concurrencyLimit);
        } else {
            // 逐个处理每个批次（串行）
            await this.processBatchesSequentially(batches, maxRetries, results);
        }

        // 统计结果
        const successCount = results.filter(r => r.success).length;
        const failCount = results.filter(r => !r.success).length;

        console.log(chalk.blue.bold('\n📊 批量购买完成'));
        console.log('═'.repeat(40));
        console.log(chalk.green(`✅ 成功: ${successCount} 个NFT`));
        console.log(chalk.red(`❌ 失败: ${failCount} 个NFT`));
        console.log(chalk.blue(`📦 总交易数: ${batches.length}`));
        console.log(chalk.blue(`🕒 总耗时: ${(new Date().getTime() - startTime) / 1000} 秒`));

        return {
            success: successCount > 0,
            totalNFTs: nftList.length,
            successCount,
            failCount,
            transactions: batches.length,
            results
        };
    }

    /**
     * 串行处理批次 - 逐个处理每个批次
     */
    async processBatchesSequentially(batches, maxRetries, results) {
        // 逐个处理每个批次（每个批次是一个包含多个NFT的交易）
        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
            const batch = batches[batchIndex];
            console.log(chalk.yellow(`\n🔄 处理交易 ${batchIndex + 1}/${batches.length} (打包 ${batch.length} 个NFT)`));

            let retryCount = 0;
            let batchSuccess = false;

            while (retryCount < maxRetries && !batchSuccess) {
                try {
                    const batchResult = await this.executePackedTransaction(batch, batchIndex + 1, retryCount + 1);

                    if (batchResult.success) {
                        batchSuccess = true;
                        results.push(...batchResult.nftResults);
                        console.log(chalk.green(`✅ 打包交易 ${batchIndex + 1} 成功完成`));
                    } else {
                        throw new Error(batchResult.error || '打包交易执行失败');
                    }

                } catch (error) {
                    // 解析和分类错误
                    const errorInfo = this.parseTransactionError(error);

                    // 检查是否是不需要重试的错误
                    if (errorInfo.shouldSkipRetry) {
                        console.log(chalk.yellow(`⚠️ 打包交易 ${batchIndex + 1}: ${errorInfo.friendlyMessage}`));
                        // 为失败的NFT添加结果记录
                        batch.forEach(nft => {
                            results.push({
                                nft_id: nft.nft_id || 'unknown',
                                nftPubkey: nft.nftPubkey,
                                success: false,
                                error: errorInfo.friendlyMessage,
                                errorCode: errorInfo.errorCode,
                                attempts: retryCount + 1,
                                skipRetry: true
                            });
                        });
                        break; // 跳出重试循环
                    }

                    retryCount++;
                    console.log(chalk.red(`❌ 打包交易 ${batchIndex + 1} 第${retryCount}次尝试失败`));
                    console.log(chalk.gray(`   错误类型: ${errorInfo.friendlyMessage}`));
                    if (errorInfo.isVerbose) {
                        console.log(chalk.gray(`   详细信息: ${errorInfo.shortMessage}`));
                    }

                    if (retryCount < maxRetries) {
                        console.log(chalk.yellow(`⏳ 等待1秒后重试...`));
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    } else {
                        console.log(chalk.red(`💥 打包交易 ${batchIndex + 1} 达到最大重试次数，跳过`));
                        // 为失败的NFT添加结果记录
                        batch.forEach(nft => {
                            results.push({
                                nft_id: nft.nft_id || 'unknown',
                                nftPubkey: nft.nftPubkey,
                                success: false,
                                error: `打包交易失败: ${error.message}`,
                                attempts: retryCount
                            });
                        });
                    }
                }
            }

            // 交易间延迟
            if (batchIndex < batches.length - 1) {
                console.log(chalk.gray('⏳ 交易间延迟 2 秒...'));
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
    }

    /**
     * 并发处理批次 - 同时处理多个批次（支持并发限制）
     */
    async processBatchesConcurrently(batches, maxRetries, results, concurrencyLimit) {
        console.log(chalk.yellow(`\n⚡ 并发处理 ${batches.length} 个打包交易...`));

        if (concurrencyLimit && concurrencyLimit < batches.length) {
            console.log(chalk.blue(`🔒 并发限制: 最多同时执行 ${concurrencyLimit} 个交易`));
            await this.processBatchesWithLimit(batches, maxRetries, results, concurrencyLimit);
            return;
        }


        // 创建所有批次的处理Promise（使用统一的processSingleBatch方法）
        const batchPromises = batches.map(async (batch, batchIndex) => {
            return await this.processSingleBatch(batch, batchIndex, maxRetries);
        });

        // 等待所有批次完成
        console.log(chalk.blue('⏳ 等待所有打包交易完成...'));
        const allBatchResults = await Promise.allSettled(batchPromises);

        // 收集所有结果
        allBatchResults.forEach((promiseResult, index) => {
            if (promiseResult.status === 'fulfilled') {
                results.push(...promiseResult.value);
            } else {
                console.log(chalk.red(`❌ 批次 ${index + 1} 处理失败: ${promiseResult.reason}`));
                // 为失败的批次添加错误结果
                const batch = batches[index];
                batch.forEach(nft => {
                    results.push({
                        nft_id: nft.nft_id || 'unknown',
                        nftPubkey: nft.nftPubkey,
                        success: false,
                        error: `批次处理失败: ${promiseResult.reason}`,
                        attempts: 0
                    });
                });
            }
        });
    }

    /**
     * 限制并发数量的批次处理
     */
    async processBatchesWithLimit(batches, maxRetries, results, concurrencyLimit) {
        console.log(chalk.blue(`🔄 分组处理: 每组最多 ${concurrencyLimit} 个并发交易`));

        // 将批次分组，每组不超过并发限制
        for (let i = 0; i < batches.length; i += concurrencyLimit) {
            const batchGroup = batches.slice(i, i + concurrencyLimit);
            const groupNumber = Math.floor(i / concurrencyLimit) + 1;
            const totalGroups = Math.ceil(batches.length / concurrencyLimit);

            console.log(chalk.cyan(`\n📦 处理第 ${groupNumber}/${totalGroups} 组 (${batchGroup.length} 个交易)`));

            // 并发处理当前组的所有批次
            const groupPromises = batchGroup.map(async (batch, localIndex) => {
                const globalIndex = i + localIndex;
                return await this.processSingleBatch(batch, globalIndex, maxRetries);
            });

            // 等待当前组完成
            const groupResults = await Promise.allSettled(groupPromises);

            // 收集结果
            groupResults.forEach((promiseResult, localIndex) => {
                const globalIndex = i + localIndex;
                if (promiseResult.status === 'fulfilled') {
                    results.push(...promiseResult.value);
                } else {
                    console.log(chalk.red(`❌ 批次 ${globalIndex + 1} 处理失败: ${promiseResult.reason}`));
                    // 为失败的批次添加错误结果
                    const batch = batchGroup[localIndex];
                    batch.forEach(nft => {
                        results.push({
                            nft_id: nft.nft_id || 'unknown',
                            nftPubkey: nft.nftPubkey,
                            success: false,
                            error: `批次处理失败: ${promiseResult.reason}`,
                            attempts: 0
                        });
                    });
                }
            });

            // 组间延迟（避免过于频繁的请求）
            if (i + concurrencyLimit < batches.length) {
                console.log(chalk.gray(`⏳ 组间延迟 10 毫秒...`));
                await new Promise(resolve => setTimeout(resolve, 10));
            }
        }
    }

    /**
     * 处理单个批次（提取公共逻辑）
     */
    async processSingleBatch(batch, batchIndex, maxRetries) {
        let retryCount = 0;
        let batchSuccess = false;
        let batchResults = [];

        while (retryCount < maxRetries && !batchSuccess) {
            try {
                const batchResult = await this.executePackedTransaction(batch, batchIndex + 1, retryCount + 1);

                if (batchResult.success) {
                    batchSuccess = true;
                    batchResults = batchResult.nftResults;
                    console.log(chalk.green(`✅ 打包交易 ${batchIndex + 1} 成功完成`));
                } else {
                    throw new Error(batchResult.error || '打包交易执行失败');
                }

            } catch (error) {
                // 解析和分类错误
                const errorInfo = this.parseTransactionError(error);

                // 检查是否是不需要重试的错误
                if (errorInfo.shouldSkipRetry) {
                    console.log(chalk.yellow(`⚠️ 打包交易 ${batchIndex + 1}: ${errorInfo.friendlyMessage}`));
                    // 为失败的NFT添加结果记录
                    batchResults = batch.map(nft => ({
                        nft_id: nft.nft_id || 'unknown',
                        nft_pubkey: nft.nft_pubkey,
                        success: false,
                        error: errorInfo.friendlyMessage,
                        errorCode: errorInfo.errorCode,
                        attempts: retryCount + 1,
                        skipRetry: true
                    }));
                    break; // 跳出重试循环
                }

                retryCount++;
                console.log(chalk.red(`❌ 打包交易 ${batchIndex + 1} 第${retryCount}次尝试失败`));
                console.log(chalk.gray(`   错误类型: ${errorInfo.friendlyMessage}`));
                if (errorInfo.isVerbose) {
                    console.log(chalk.gray(`   详细信息: ${errorInfo.shortMessage}`));
                }

                if (retryCount < maxRetries) {
                    console.log(chalk.yellow(`⏳ 交易 ${batchIndex + 1} 等待1秒后重试...`));
                    await new Promise(resolve => setTimeout(resolve, 10));
                } else {
                    console.log(chalk.red(`💥 打包交易 ${batchIndex + 1} 达到最大重试次数，跳过`));
                    // 为失败的NFT添加结果记录
                    batchResults = batch.map(nft => ({
                        nft_id: nft.nft_id || 'unknown',
                        nftPubkey: nft.nftPubkey,
                        success: false,
                        error: `打包交易失败: ${error.message}`,
                        attempts: retryCount
                    }));
                }
            }
        }

        return batchResults;
    }

    /**
     * 执行打包交易 - 将多个NFT的Buy指令打包到一个交易中
     * @param {Array} nftBatch - 要打包的NFT列表
     * @param {number} transactionNumber - 交易编号
     * @param {number} attemptNumber - 尝试次数
     */
    async executePackedTransaction(nftBatch, transactionNumber, attemptNumber) {
        try {
            // console.log(chalk.blue(`  📦 打包交易 ${transactionNumber} 第${attemptNumber}次尝试:`));
            // nftBatch.forEach((nft, index) => {
            //     console.log(chalk.gray(`    ${index + 1}. NFT ${nft.nft_id || 'unknown'} (${nft.nftPubkey.substring(0, 8)}...)`));
            // });

            // 构建包含多个Buy指令的单个交易
            const transaction = new Transaction();

            // console.log(chalk.blue(`  🔧 构建包含 ${nftBatch.length} 个Buy指令的交易...`));

            // 为每个NFT创建Buy指令并添加到同一个交易中
            for (const nft of nftBatch) {
                // console.log(chalk.gray(`    ⚙️ 创建NFT ${nft.nft_id || 'unknown'} 的Buy指令...`));

                const buyInstruction = await this.createBuyInstruction({
                    project_pubkey: new PublicKey(nft.project_pubkey),
                    creator_pubkey: new PublicKey(nft.creator_pubkey),
                    authority_pubkey: new PublicKey(nft.authority_pubkey),
                    mint_pubkey: new PublicKey(nft.mint_pubkey),
                    nft_pubkey: new PublicKey(nft.nft_pubkey),
                    buyer: this.wallet.publicKey,
                    owner_pubkey: new PublicKey(nft.owner_pubkey),
                    nft_id: nft.nft_id

                });

                transaction.add(buyInstruction);
                // console.log(chalk.gray(`    ✅ NFT ${nft.nft_id || 'unknown'} 指令已添加`));
            }

            // console.log(JSON.stringify(transaction));

            // console.log(chalk.blue(`  📋 交易包含 ${transaction.instructions.length} 个指令`));

            // 获取最新的区块哈希
            const { blockhash } = await this.connection.getLatestBlockhash();
            transaction.recentBlockhash = blockhash;
            transaction.feePayer = this.wallet.publicKey;

            // 签名交易
            // console.log(chalk.blue(`  ✍️ 签名打包交易...`));
            transaction.sign(this.wallet);

            // 发送交易
            // console.log(chalk.blue(`  📤 发送打包交易 (${nftBatch.length} 个NFT在一个交易中)...`));
            const signature = await this.connection.sendRawTransaction(transaction.serialize(), {
                skipPreflight: false,
                preflightCommitment: 'confirmed'
            });

            console.log(chalk.green(`  📝 打包交易签名: ${signature}`));

            // 等待确认
            console.log(chalk.blue(`  ⏳ 等待交易确认...`));
            const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');

            if (confirmation.value.err) {
                throw new Error(`打包交易失败: ${JSON.stringify(confirmation.value.err)}`);
            }

            console.log(chalk.green(`  ✅ 打包交易确认成功! 所有 ${nftBatch.length} 个NFT已购买`));

            // 为交易中的每个NFT创建成功结果
            const nftResults = nftBatch.map(nft => ({
                nft_id: nft.nft_id || 'unknown',
                nftPubkey: nft.nftPubkey,
                success: true,
                signature: signature,
                transactionNumber: transactionNumber,
                attempts: attemptNumber,
                packedWith: nftBatch.length // 标记这是打包交易
            }));

            return {
                success: true,
                signature,
                nftResults,
                transactionNumber,
                packedCount: nftBatch.length
            };

        } catch (error) {
            // console.log(chalk.red(`  ❌ 打包交易执行失败: ${error.message}`));
            return {
                success: false,
                error: error.message,
                transactionNumber
            };
        }
    }

    /**
     * 解析交易错误，提供友好的错误信息
     */
    parseTransactionError(error) {
        const errorMessage = error.message || '';

        // PPP协议特定错误
        if (errorMessage.includes('NFTSoldOutThisRound') || errorMessage.includes('NFT already traded this round')) {
            return {
                errorType: 'PPP协议错误',
                errorCode: 'NFTSoldOutThisRound',
                friendlyMessage: 'NFT在当前轮次已被交易，需等待下一轮',
                shortMessage: '当前轮次已交易',
                shouldSkipRetry: false,
                isVerbose: false
            };
        }

        // 账户不匹配错误
        if (errorMessage.includes('Owner account mismatch')) {
            return {
                errorType: '所有权错误',
                errorCode: 'OwnerMismatch',
                friendlyMessage: 'NFT已被他人购买',
                shortMessage: '所有者已变更',
                shouldSkipRetry: true,
                isVerbose: false
            };
        }

        // 网络限流错误
        if (errorMessage.includes('429 Too Many Requests')) {
            return {
                errorType: '网络限流',
                errorCode: 'RateLimit',
                friendlyMessage: '请求过于频繁，稍后重试',
                shortMessage: 'API限流中',
                shouldSkipRetry: false,
                isVerbose: false
            };
        }

        // 模拟失败错误
        if (errorMessage.includes('Simulation failed')) {
            // 提取关键错误信息
            const logMatch = errorMessage.match(/Error Message: ([^.]+)/);
            const shortMsg = logMatch ? logMatch[1] : '交易模拟失败';

            return {
                errorType: '交易模拟失败',
                errorCode: 'SimulationFailed',
                friendlyMessage: shortMsg,
                shortMessage: shortMsg,
                shouldSkipRetry: false,
                isVerbose: true
            };
        }

        // 其他错误
        return {
            errorType: '未知错误',
            errorCode: 'Unknown',
            friendlyMessage: errorMessage.length > 100 ? errorMessage.substring(0, 100) + '...' : errorMessage,
            shortMessage: errorMessage.length > 50 ? errorMessage.substring(0, 100) + '...' : errorMessage,
            shouldSkipRetry: false,
            isVerbose: true
        };
    }

    /**
     * 创建PPP Buy指令 (基于真实交易数据)
     */
    async createBuyInstruction(params) {
        // const { mint_pubkey, nft_pubkey, buyer, owner_pubkey,nft_id} = params;

        // 使用真正的Buy指令discriminator (8字节)
        // 不包含金额参数，价格由PPP程序内部计算
        // const instructionData = Buffer.from('66063d1201daebea', 'hex');
        const instructionData = Buffer.from([102,6,61,18,1,218,235,234]);



        const builder = new PPPInstructionBuilder();
        const buildResult = await builder.buildBuyInstructionAccounts(params);

        //   console.log('buildResult:', buildResult);


        let keys = buildResult.accounts;
        //    console.log("======")

        //输出 keys
        // console.log('keys:', keys);
        return new TransactionInstruction({
            keys,
            programId: PPP_PROGRAM_ID,
            data: instructionData
        });
    }

}



export { PPPBuyScript };

// 如果直接运行此脚本

// main().catch(console.error);


