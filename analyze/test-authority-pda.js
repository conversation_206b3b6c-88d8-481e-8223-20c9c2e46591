#!/usr/bin/env node

import { PublicKey } from '@solana/web3.js';
import chalk from 'chalk';

/**
 * 测试不同种子组合计算authority PDA的脚本
 * 目标是找出正确的种子组合，使得计算出的PDA地址为：8KmFDMPBrCBDsqrr8BdKe1XCeGwhz8Si8Euj1PXLw9jW
 */
async function testAuthorityPDA() {
    console.log(chalk.blue.bold('🔍 测试不同种子组合计算authority PDA\n'));

    // 已知的正确authority地址
    const expectedAuthority = new PublicKey('8KmFDMPBrCBDsqrr8BdKe1XCeGwhz8Si8Euj1PXLw9jW');
    console.log(chalk.yellow(`目标authority地址: ${expectedAuthority.toString()}`));

    // 已知的项目相关地址
    const programId = new PublicKey('PPPtBKg4ggAdZ1KRKv7wGEJoUq8BX79Eh4fKEkgQsJ7');
    const projectPubkey = new PublicKey('CtwYe2yWZ7mB95PnG5TUupkta5Apf1j17UwHomrsjiH1');
    const mintPubkey = new PublicKey('4TStMHaXQhkqvFRXq8dmMLyP3VYmkdAuCYBBPMv2Wppp');
    const creatorPubkey = new PublicKey('3KYpJ3Pc34QL7c52jgW7ovF9gtpLEEwSv7Hs37J35YSm');

    console.log(chalk.blue('\n📊 测试数据:'));
    console.log(chalk.gray(`程序ID: ${programId.toString()}`));
    console.log(chalk.gray(`项目地址: ${projectPubkey.toString()}`));
    console.log(chalk.gray(`代币地址: ${mintPubkey.toString()}`));
    console.log(chalk.gray(`创建者地址: ${creatorPubkey.toString()}`));

    // 测试不同的种子组合
    console.log(chalk.blue('\n🧪 测试不同的种子组合:'));

    // 测试组合1: ["ppp_auth", mint_pubkey]
    testPDASeeds(
        "组合1: [\"ppp_auth\", mint_pubkey]",
        programId,
        [Buffer.from("ppp_auth"), mintPubkey.toBuffer()],
        expectedAuthority
    );

   
   

    // 测试组合5: ["ppp_auth", project_pubkey, mint_pubkey]

}

/**
 * 测试给定种子组合计算PDA地址
 * @param {string} testName 测试名称
 * @param {PublicKey} programId 程序ID
 * @param {Buffer[]} seeds 种子数组
 * @param {PublicKey} expectedPDA 预期的PDA地址
 */
function testPDASeeds(testName, programId, seeds, expectedPDA) {
    try {
        const [pdaAddress, bump] = PublicKey.findProgramAddressSync(seeds, programId);
        
        const isMatch = pdaAddress.equals(expectedPDA);
        
        if (isMatch) {
            console.log(chalk.green(`✅ ${testName} - 匹配成功!`));
            console.log(chalk.green(`   PDA地址: ${pdaAddress.toString()}`));
            console.log(chalk.green(`   Bump: ${bump}`));
        } else {
            console.log(chalk.red(`❌ ${testName} - 不匹配`));
            console.log(chalk.red(`   计算结果: ${pdaAddress.toString()}`));
            console.log(chalk.red(`   预期结果: ${expectedPDA.toString()}`));
            console.log(chalk.red(`   Bump: ${bump}`));
        }
    } catch (error) {
        console.log(chalk.red(`❌ ${testName} - 出错: ${error.message}`));
    }
}

// 执行测试
testAuthorityPDA().catch(console.error);